package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface AgreementSignatureRecordRepository extends JpaRepository<AgreementSignatureRecord, String> {
    List<AgreementSignatureRecord> findByUserIdAndLoanStage(String userId, LoanStage stage);

    List<AgreementSignatureRecord> findBySignState(ProcessState processing);

    List<AgreementSignatureRecord> findByIdIn(List<String> signApplyIdList);

    List<AgreementSignatureRecord> findBySignStateAndCreatedTimeBetween(ProcessState signState, LocalDateTime beginTime, LocalDateTime endTime);

    List<AgreementSignatureRecord> findByCreatedTimeBetweenAndSignStateIn(LocalDateTime beginTime, LocalDateTime endTime, ProcessState... signStates);

    List<AgreementSignatureRecord> findByCreatedTimeBetweenAndRemark(LocalDateTime beginTime, LocalDateTime endTime, String remark);

    @Query(value = "SELECT s.id FROM Order o "
        + " LEFT JOIN AgreementSignRelation a on o.riskId = a.relatedId"
        + " LEFT JOIN AgreementSignatureRecord s on a.signApplyId = s.id"
        + " where  o.orderState = ?4 and o.createdTime >= ?1"
        + " and o.createdTime <= ?2 and s.fileType = ?3"
    )
    List<String> findByCreatedTimeBetweenAndFileType(LocalDateTime beginTime, LocalDateTime endTime, FileType fileType,
                                                     OrderState orderState);
}
