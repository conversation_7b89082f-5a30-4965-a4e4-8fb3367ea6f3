package com.maguo.loan.cash.flow.service.common.loan;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.LoanConvert;
import com.maguo.loan.cash.flow.dto.LoanSuspendFlag;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.ProjectLimitConfig;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.FlowConfigRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.ProjectLimitConfigRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> gale
 * @Classname LoanCommonService
 * @Description 放款组件服务类
 * @Date 2024/1/5 17:29
 */
@Service
public class LoanCommonService {

    private static final Logger logger = LoggerFactory.getLogger(LoanCommonService.class);

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private CacheService cacheService;

    private static final long LIMIT_EXPIRE_TIME = 24;

    @Autowired
    private MqService mqService;

    @Autowired
    private LockService lockService;

    @Autowired
    private CapitalConfigRepository capitalConfigRepository;

    @Autowired
    private WarningService warningService;

    @Autowired
    private FlowConfigRepository flowConfigRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;
    @Autowired
    private LoanRecordRepository loanRecordRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private ProjectLimitConfigRepository projectLimitConfigRepository;

    private static final int AUTO_ROUTE_HOURS = 48;

    private static final String PREFIX_CREDIT = "CREDIT";

    private static final String PREFIX_LOAN = "LOAN";

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");


    public Loan initLoan(String creditId) {
        logger.info("初始化放款订单 loan " + creditId);
        Credit credit = creditRepository.findById(creditId).orElseThrow();

        //判断授信状态
        if (ProcessState.SUCCEED != credit.getState()) {
            warningService.warn("授信未成功,不能发起放款,creditId:" + creditId, logger::error);
            throw new BizException(ResultCode.CREDIT_NOT_SUCCEED);
        }
        Loan loan = loanRepository.findByCreditId(creditId);
        if (Objects.nonNull(loan)) {
            if (ProcessState.PROCESSING != loan.getLoanState()
                    || loanRecordRepository.existsByOrderIdAndLoanStateNotIn(credit.getOrderId(), ProcessState.FAILED)) {
                //存在非失败的放款记录
                warningService.warn("存在非失败的放款(记录),不能发起放款,orderId:" + credit.getOrderId(), logger::error);
                throw new BizException(ResultCode.NOT_FAIL_LOAN_RECORD_EXIST);
            }
        }

        loan = loanRepository.findByOrderId(credit.getOrderId());
        if (Objects.isNull(loan)) {
            //新增借据
            Order order = orderRepository.findById(credit.getOrderId()).orElseThrow();
            //
            loan = LoanConvert.INSTANCE.toLoan(credit);
            loan.setApplyChannel(order.getApplyChannel());
            loan.setApplyTime(LocalDateTime.now());
            loan.setRepayCardId(credit.getLoanCardId());
            loan.setLoanState(ProcessState.INIT); //放款状态
            loan.setPlanSyncCore(WhetherState.N);
            // 权益参数
            rightsSetting(loan, order);
            //保存是不是权益客户
            if(FlowChannel.LVXIN.equals(order.getFlowChannel())){
                loan.setIsIncludingEquity(order.getIsIncludingEquity());
                loan.setEquityRecipient(order.getEquityRecipient());
            }
            // 是否复贷
            boolean isReloan = isReloan(order);

            loan.setReloan(isReloan ? WhetherState.Y : WhetherState.N);
        } else {
            //更新借据
            loan.setCreditId(creditId);
            loan.setBankChannel(credit.getBankChannel());
            loan.setGuaranteeCompany(credit.getGuaranteeCompany());
            loan.setIrrRate(credit.getIrrRate());
            loan.setLoanCardId(credit.getLoanCardId());
            loan.setLoanNo(null);
        }

        return loanRepository.save(loan);
    }

    private boolean isReloan(Order order) {
        // 定义所有需要检查的流渠道
        List<Loan> loans = loanRepository.findByUserIdAndLoanState(order.getUserId(), ProcessState.SUCCEED);

        if (CollectionUtil.isEmpty(loans)) {
            return false;
        }

        return loans.stream().anyMatch(loan -> loan.getFlowChannel().equals(order.getFlowChannel()));
    }

    private void rightsSetting(Loan loan, Order order) {
        if (WhetherState.Y == order.getRightsMarking()) {
            loan.setPackageId(order.getRightsPackageId()); //权益包id
            loan.setApproveRights(order.getApproveRights());
            loan.setRightsAmount(order.getRightsAmount());
        } else {
            loan.setPackageId(""); //权益包id
            loan.setApproveRights(RightsLevel.NONE);
            loan.setRightsAmount(BigDecimal.ZERO);
        }
        loan.setRightsDeductState(WhetherState.N);
    }


    /**
     * 挂起校验
     *
     * @return
     */
    public LoanSuspendFlag suspendCheck(Loan loan) {
        logger.info("挂起校验 suspendCheck:{}", JsonUtil.toJsonString(loan));
        // 时间限制校验
        if (timeSuspend(loan.getBankChannel())) {
            return LoanSuspendFlag.SUSPEND;
        }
        // 额度限制校验
        LoanSuspendFlag limitFlag = limitSuspend(loan);
        if (limitFlag == LoanSuspendFlag.SUSPEND || limitFlag == LoanSuspendFlag.IGNORE) {
            return limitFlag;
        }
        // 授信有效期校验
        if (creditSuspend(loan)) {
            return LoanSuspendFlag.SUSPEND;
        }
        return LoanSuspendFlag.RECOVER;
    }

    /**
     * 时间限制校验
     *
     * @return
     */
    private boolean timeSuspend(BankChannel bankChannel) {
        // 资方配置
        CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannel(bankChannel)
                .orElseThrow(() -> new BizException(ResultCode.ROURE_CAPITAL_CONFIG_NOT_EXIST));
        if (StringUtil.isBlank(capitalConfig.getLoanStartTime()) || StringUtil.isBlank(capitalConfig.getLoanEndTime())) {
            return false;
        }

        LocalTime currentTime = LocalTime.now();
        boolean after = currentTime.isAfter(LocalTime.parse(capitalConfig.getLoanEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss")));
        boolean before = currentTime.isBefore(LocalTime.parse(capitalConfig.getLoanStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss")));
        if (after || before) {
            logger.info("当前资方放款限制时间:{}", bankChannel);
            return true;
        }
        return false;
    }

    /**
     * 额度限制校验
     *
     * @param loan
     * @return
     */
    private LoanSuspendFlag limitSuspend(Loan loan) {
        BankChannel bankChannel = loan.getBankChannel();

        // 资金额度检测加锁
        Locker lock = lockService.getLock(RedisKeyConstants.BIZ_LIMIT_LOAN + bankChannel.name());
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.info("limitSuspend: [{}], 资方:{}, 未获取到锁,不校验额度", loan.getId(), bankChannel);
                return LoanSuspendFlag.RECOVER;
            }

            //判断资方是否禁用/放款额度
            CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannel(bankChannel).orElseThrow();
            AbleStatus capitalStatus = capitalConfig.getEnabled();
            BigDecimal loanDayLimit = capitalConfig.getLoanDayLimit();

            //资金方停用后，当停用时间达到48小时时，自动路由下个资方
            if (AbleStatus.DISABLE == capitalStatus && capitalConfig.getUpdatedTime().until(LocalDateTime.now(), ChronoUnit.HOURS) >= AUTO_ROUTE_HOURS) {
                logger.info("limitSuspend: [{}], 资方:{}, 资方禁用已达到48小时,继续下个路由", loan.getId(), bankChannel);
                loan.setLoanState(ProcessState.INIT);
                loanRepository.save(loan);

                // 路由到下个资方授信
                mqService.submitCreditRouteApply(loan.getOrderId());
                return LoanSuspendFlag.IGNORE;
            }

            if (AbleStatus.DISABLE == capitalStatus || BigDecimal.ZERO.compareTo(loanDayLimit) == 0) {
                logger.info("limitSuspend: [{}], 资方:{}, 状态:{},额度:{}", loan.getId(), bankChannel, capitalStatus, loanDayLimit);
                return LoanSuspendFlag.SUSPEND;
            }

            // 资方放款日剩余限额(key)
            String capitalLimitKey = RedisKeyConstants.BIZ_LIMIT_LOAN + bankChannel.name() + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);

            //日剩余额度
            Object limitAmtObj = cacheService.get(capitalLimitKey);
            BigDecimal limitAmt = limitAmtObj == null ? BigDecimal.ZERO : new BigDecimal(String.valueOf(limitAmtObj));
            logger.info("limitSuspend: [{}], 资方:{}, redis剩余额度:{}", loan.getId(), bankChannel, limitAmt);
            if (limitAmt.compareTo(loan.getAmount()) <= 0) {
                //额度不足
                //重新计算剩余额度
                limitAmt = getLimitAmtByDB(bankChannel, loanDayLimit);
                logger.info("limitSuspend: [{}], 资方:{}, 计算db剩余额度:{}", loan.getId(), bankChannel, limitAmt);
            }

            if (limitAmt.compareTo(loan.getAmount()) <= 0) {
                //额度重新计算后仍然不足
                logger.info("limitSuspend: [{}], 资方:{}, 剩余额度为:{}, 放款金额为:{}, 额度不足", loan.getId(), loan.getBankChannel(), limitAmt, loan.getAmount());
                return LoanSuspendFlag.SUSPEND;
            } else {
                //额度充足
                //更新剩余额度
                logger.info("limitSuspend: [{}], 资方:{}, 剩余额度为:{}, 放款金额为:{}, 额度充足,允许放款", loan.getId(), loan.getBankChannel(), limitAmt,
                    loan.getAmount());
                cacheService.put(capitalLimitKey, limitAmt.subtract(loan.getAmount()), LIMIT_EXPIRE_TIME, TimeUnit.HOURS);
                return LoanSuspendFlag.RECOVER;
            }
        } catch (Exception e) {
            logger.error("放款挂起校验异常:", e);
        } finally {
            lock.unlock();
        }

        return LoanSuspendFlag.RECOVER;
    }

    private BigDecimal getUsedAmountFromDB(String prefix, ProjectLimitConfig config) {
        LocalDate currentDate = LocalDate.now();
        LocalDateTime todayStart = LocalDateTime.of(currentDate, LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(currentDate, LocalTime.MAX);

        FlowChannel flowChannelEnum = FlowChannel.valueOf(config.getFlowChannel());
        GuaranteeCompany guaranteeCompEnum = GuaranteeCompany.valueOf(config.getGuaranteeComp());
        BankChannel bankChannelEnum = BankChannel.valueOf(config.getBankChannel());

        BigDecimal succeedAmt = BigDecimal.ZERO;
        if (PREFIX_LOAN.equals(prefix)) {
            succeedAmt = loanRepository.sumAmountByDimensionsAndStates(
                flowChannelEnum, guaranteeCompEnum, bankChannelEnum,
                ProcessState.SUCCEED,
                todayStart, todayEnd
            ).orElse(BigDecimal.ZERO);

        } else if (PREFIX_CREDIT.equals(prefix)) {
            if (flowChannelEnum.equals(FlowChannel.LVXIN)) {
                succeedAmt = preOrderRepository.sumAmountByDimensionsAndStates(
                    flowChannelEnum, bankChannelEnum,
                    PreOrderState.AUDIT_PASS,
                    todayStart, todayEnd
                ).orElse(BigDecimal.ZERO);
            } else {
                succeedAmt = preOrderRepository.sumAmountByStates(
                    flowChannelEnum,
                    PreOrderState.AUDIT_PASS,
                    todayStart, todayEnd
                ).orElse(BigDecimal.ZERO);
            }
        }
        return succeedAmt;
    }
    /**
     * 授信有效期校验
     */
    private boolean creditSuspend(Loan loan) {
        switch (loan.getBankChannel()) {
            default -> {
                return false;
            }
        }
    }

    /**
     * 更新额度
     *
     * @param key
     * @param channel
     * @param loanAmount 申请金额
     */
    private void updateLimit(String key, BankChannel channel, BigDecimal loanAmount) {
        LocalDate currentDate = LocalDate.now();
        // 已成功金额
        BigDecimal succeedAmt = loanRepository.dayOccupyLoanAmountSucceed(channel, List.of(ProcessState.SUCCEED),
                LocalDateTime.of(currentDate, LocalTime.MIN), LocalDateTime.of(currentDate, LocalTime.MAX));
        // 处理中占用金额
        BigDecimal processingAmt = loanRepository.dayOccupyLoanAmountProcessing(channel, List.of(ProcessState.INIT, ProcessState.PROCESSING),
                LocalDateTime.of(currentDate, LocalTime.MIN), LocalDateTime.of(currentDate, LocalTime.MAX));
        // 占用总金额
        BigDecimal totalAmt = AmountUtil.sum(succeedAmt, processingAmt);

        CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannel(channel)
                .orElseThrow(() -> new BizException(ResultCode.ROURE_CAPITAL_CONFIG_NOT_EXIST));
        BigDecimal limitLoanDay = capitalConfig.getLoanDayLimit();
        if (Objects.isNull(limitLoanDay)) {
            // 默认放款限额
            limitLoanDay = new BigDecimal("5000000");
        }
        // 计算剩余限额
        BigDecimal limitAmt = limitLoanDay.subtract(totalAmt).subtract(loanAmount);
        // 将剩余限额缓存起来
        cacheService.put(key, limitAmt, LIMIT_EXPIRE_TIME, TimeUnit.HOURS);
    }


    /**
     * 计算db剩余放款额度
     *
     * @param channel      资方
     * @param loanDayLimit 资方日限额
     * @return 日剩余额度
     */
    private BigDecimal getLimitAmtByDB(BankChannel channel, BigDecimal loanDayLimit) {
        LocalDate currentDate = LocalDate.now();
        // 已成功金额
        BigDecimal succeedAmt = loanRepository.dayOccupyLoanAmountSucceed(channel, List.of(ProcessState.SUCCEED),
                LocalDateTime.of(currentDate, LocalTime.MIN), LocalDateTime.of(currentDate, LocalTime.MAX));
        // 处理中占用金额
        BigDecimal processingAmt = loanRepository.dayOccupyLoanAmountProcessing(channel, List.of(ProcessState.INIT, ProcessState.PROCESSING),
                LocalDateTime.of(currentDate, LocalTime.MIN), LocalDateTime.of(currentDate, LocalTime.MAX));
        // 占用总金额
        BigDecimal totalAmt = AmountUtil.sum(succeedAmt, processingAmt);

        // 计算剩余限额
        return loanDayLimit.subtract(totalAmt);
    }

    /**
     * 计算db流量剩余放款额度
     *
     * @param channel      资方
     * @param loanDayLimit 资方日限额
     * @return 日剩余额度
     */
    private BigDecimal getLimitAmtByDBAndFlowChannel(FlowChannel channel, BigDecimal loanDayLimit) {
        LocalDate currentDate = LocalDate.now();
        // 已成功金额
        BigDecimal succeedAmt = loanRepository.dayOccupyLoanAndFlowChanelAmountSucceed(channel, List.of(ProcessState.SUCCEED),
                LocalDateTime.of(currentDate, LocalTime.MIN), LocalDateTime.of(currentDate, LocalTime.MAX));
        // 处理中占用金额
        BigDecimal processingAmt = loanRepository.dayOccupyLoanAmountAndFlowChanelProcessing(channel, List.of(ProcessState.INIT, ProcessState.PROCESSING),
                LocalDateTime.of(currentDate, LocalTime.MIN), LocalDateTime.of(currentDate, LocalTime.MAX));
        // 占用总金额
        BigDecimal totalAmt = AmountUtil.sum(succeedAmt, processingAmt);

        // 计算剩余限额
        return loanDayLimit.subtract(totalAmt);
    }


    /**
     * 订单挂起激活
     */
    public void suspendActive(String loanId) {
        Locker lock = lockService.getLock(RedisKeyConstants.SUSPEND_ACTIVE_LOAN + loanId);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (locked) {
                Loan loan = loanRepository.findById(loanId).orElseThrow();
                if (ProcessState.SUSPEND != loan.getLoanState()) {
                    //非挂起状态，忽略
                    return;
                }
                //校验order状态
                Order order = orderRepository.findOrderById(loan.getOrderId());
                OrderState orderState = order.getOrderState();
                if (OrderState.LOANING != orderState) {
                    warningService.warn("放款激活异常,loanId:" + loanId + ",orderId:" + order.getId() + ",订单状态为" + orderState, logger::error);
                    //订单状态异常，忽略
                    return;
                }

                LoanSuspendFlag loanSuspendFlag = suspendCheck(loan);
                switch (loanSuspendFlag) {
                    case IGNORE -> {
                        return;
                    }
                    case RECOVER -> {
                        //继续放款
                        loan.setLoanState(ProcessState.INIT);
                        loan.setApplyTime(LocalDateTime.now());
                        loanRepository.save(loan);
                        // 申请放款
                        mqService.submitLoanApply(loan.getId());
                    }
                    default -> {
                        logger.info("当前借据:{} 激活失败,保持挂起状态", loan.getId());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("挂起激活异常,error:{},loan:{}", e.getMessage(), loanId);
        } finally {
            lock.unlock();
        }
    }


    public void loanFail(Loan loan) {
        loan.setLoanState(ProcessState.FAILED);
        loan = loanRepository.save(loan);

        //推送放款失败事件
        eventPublisher.publishEvent(new LoanResultEvent(loan.getId(), loan.getLoanState(), loan.getLoanTime(), loan.getBankChannel(),
                loan.getFailReason()));
    }


    /**
     * 流量放款日限额
     *
     */
    public boolean limitDayLoanFlowChannel(Order order) {

        String flowChannelStr = order.getFlowChannel().name();
        String bankChannelStr = order.getBankChannel() != null ? order.getBankChannel().name() : null;
        ProjectLimitConfig projectConfig;
        if (flowChannelStr.equals("LVXIN")) {
            projectConfig = projectLimitConfigRepository.findByFlowChannelAndBankChannel(flowChannelStr, bankChannelStr)
                .orElse(null);
        } else {
            projectConfig = projectLimitConfigRepository.findByFlowChannel(flowChannelStr)
                .orElse(null);
        }
        if (projectConfig == null || projectConfig.getLoanDayLimit().compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("limitDayLoanFlowChannel: [{}], 项目已禁用或放款日限额为0，判定为额度不足.", order.getId());
            return true;
        }
        BigDecimal loanAmount = order.getApplyAmount();

        String redisKey;
        FlowChannel flowChannel = order.getFlowChannel();

        if (FlowChannel.LVXIN == flowChannel) {
            redisKey = buildLimitLvXinRedisKey(PREFIX_LOAN, projectConfig);
            logger.info("limitDayLoanFlowChannel: [{}], 检测到渠道为[LVXIN], 使用绿信RedisKey: [{}]", order.getId(), redisKey);
        } else {
            redisKey = buildLimitPaiPaiRedisKey(PREFIX_LOAN, projectConfig);
            logger.info("limitDayLoanFlowChannel: [{}], 检测到渠道为[{}], 使用拍拍RedisKey: [{}]", order.getId(), flowChannel.name(), redisKey);
        }
        String lockKey = redisKey + ":lock";
        Locker lock = lockService.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.warn("limitDayLoanFlowChannel: [{}], 未获取到额度锁 [{}], 本次不校验，临时允许通过.", order.getId(), lockKey);
                return false;
            }

            BigDecimal loanDayLimit = projectConfig.getLoanDayLimit();
            logger.info("limitDayLoanFlowChannel: [{}], 开始校验. Key: [{}], 总限额: [{}]", order.getId(), redisKey, loanDayLimit);
            Object limitAmtObj = cacheService.get(redisKey);
            BigDecimal remainingAmt;
            if (limitAmtObj == null) {
                logger.info("limitDayLoanFlowChannel: [{}], 缓存未命中，从DB加载已用额度. Key: [{}]", order.getId(), redisKey);
                BigDecimal usedAmount = getUsedAmountFromDB(PREFIX_LOAN, projectConfig);
                remainingAmt = loanDayLimit.subtract(usedAmount);
                logger.info("limitDayLoanFlowChannel: [{}], DB已用: [{}], 计算后初始剩余: [{}]", order.getId(), usedAmount, remainingAmt);
            } else {
                remainingAmt = new BigDecimal(String.valueOf(limitAmtObj));
                logger.info("limitDayLoanFlowChannel: [{}], 缓存命中，当前Redis剩余: [{}]", order.getId(), remainingAmt);
            }

            if (remainingAmt.compareTo(loanAmount) < 0) {
                logger.info("limitDayLoanFlowChannel: [{}], Redis缓存额度不足，从DB二次确认. 剩余: [{}], 需要: [{}]",
                    order.getId(), remainingAmt, loanAmount);
                BigDecimal usedAmount = getUsedAmountFromDB(PREFIX_LOAN, projectConfig);
                remainingAmt = loanDayLimit.subtract(usedAmount);
                logger.info("limitDayLoanFlowChannel: [{}], DB二次确认后剩余: [{}]", order.getId(), remainingAmt);
            }

            if (remainingAmt.compareTo(loanAmount) < 0) {
                logger.warn("limitDayLoanFlowChannel: [{}], 最终额度不足，校验不通过. 剩余: [{}], 需: [{}].",
                    order.getId(), remainingAmt, loanAmount);
                cacheService.put(redisKey, remainingAmt.max(BigDecimal.ZERO), 24, TimeUnit.HOURS);
                return true;
            }

            BigDecimal newRemainingAmt = remainingAmt.subtract(loanAmount);
            logger.info("limitDayLoanFlowChannel: [{}], 额度充足，校验通过. 更新后剩余: [{}]", order.getId(), newRemainingAmt);
            cacheService.put(redisKey, newRemainingAmt, 24, TimeUnit.HOURS);
            return false;

        } catch (Exception e) {
            logger.error("limitDayLoanFlowChannel: [{}], 校验时发生未知异常，为安全起见，判定为额度不足.", order.getId(), e);
            return true;
        } finally {
            lock.unlock();
        }
    }


    private String buildLimitLvXinRedisKey(String prefix, ProjectLimitConfig config) {
        return String.join(":",
            prefix,
            config.getFlowChannel(),
            config.getGuaranteeComp(),
            config.getBankChannel(),
            LocalDate.now().format(DATE_FORMATTER)
        );
    }
    private String buildLimitPaiPaiRedisKey(String prefix, ProjectLimitConfig config) {
        return String.join(":",
            prefix,
            config.getFlowChannel(),
            config.getGuaranteeComp(),
            LocalDate.now().format(DATE_FORMATTER)
        );
    }


}
