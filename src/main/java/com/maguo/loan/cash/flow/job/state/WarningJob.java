package com.maguo.loan.cash.flow.job.state;


import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
@JobHandler("warningJob")
public class WarningJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(WarningJob.class);

   @Resource(name = "warningStateService")
    private WarningService warningService;

    @Autowired
    private LoanRecordRepository loanRecordRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private LoanRepository loanRepository;

    /**
     * 达到阈值时@指定人
     */
    @Value("${warning.serviceData.at:}")
    private String warningAt;


    @Value("${warningJob.minusHours}")
    private Long subtractionHours;

    @Value("${warningJob.size}")
    private Integer size;

    private static final int ONE_HUNDRED = 100;

    private static final int THREE_HUNDRED = 300;


    /**
     * 告警记录：查询预定单表 pre_order
     * 授信表：credit
     * 放款表：loan
     * 对资还款计划表：BankRepayRecord
     * 对客还款记录：CustomRepayRecord
     * @param jobParam
     */
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("warningJob start");

        boolean isAt = false;

        LocalDateTime end = LocalDateTime.now().minusHours(1L);
        LocalDateTime start = end.minusHours(subtractionHours);
        logger.info(" 查询开始时间为:{},结束时间为:{}", start, end);
        StringBuilder sb = new StringBuilder();


        /*
        对资还款记录
         */
        List<BankRepayRecord> bankRepayRecord = bankRepayRecordRepository
            .findByCreatedTimeBetweenAndStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (bankRepayRecord.size() >= size) {

            String result = bankRepayRecord.stream()
                .map(BankRepayRecord::getId)  // 提取name字段
                .collect(Collectors.joining(", "));
            sb.append(message(start, end, "bank_repay_record", bankRepayRecord.size(),result)).append("\n").append("\n");

            if (bankRepayRecord.size() >= ONE_HUNDRED) {
                isAt = true;
            }
        }
        /*
        授信记录
         */
        List<Credit> credit = creditRepository
            .findByApplyTimeBetweenAndStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (credit.size() >= size) {
            String result = credit.stream()
                .map(Credit::getId)  // 提取name字段
                .collect(Collectors.joining(", "));
            sb.append(message(start, end, "credit", credit.size(),result)).append("\n").append("\n");
            if (credit.size() >= ONE_HUNDRED) {
                isAt = true;
            }
        }
        /*
        对客还款记录
         */
        List<CustomRepayRecord> customRepayRecord = customRepayRecordRepository
            .findByRepayApplyDateBetweenAndRepayStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (customRepayRecord.size() >= size) {
            String result = customRepayRecord.stream()
                .map(CustomRepayRecord::getId)  // 提取name字段
                .collect(Collectors.joining(", "));
            sb.append(message(start, end, "custom_repay_record", customRepayRecord.size(),result)).append("\n").append("\n");
            if (customRepayRecord.size() >= THREE_HUNDRED) {
                isAt = true;
            }
        }
        /*
        放款记录
         */

        List<Loan> loan = loanRepository
            .findByApplyTimeBetweenAndLoanStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (loan.size() >= size) {
            String result = loan.stream()
                .map(Loan::getId)  // 提取name字段
                .collect(Collectors.joining(", "));
            sb.append(message(start, end, "loan_record", loan.size(),result));

            if (loan.size() >= ONE_HUNDRED) {
                isAt = true;
            }
        }

        /*
        预订单记录
         */

        List<PreOrder> preOrder = preOrderRepository
            .findByApplyTimeBetweenAndPreOrderStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (preOrder.size() >= size) {
            String result = preOrder.stream()
                .map(PreOrder::getId)  // 提取name字段
                .collect(Collectors.joining(", "));
            sb.append(message(start, end, "preOrder_record", preOrder.size(),result));

            if (preOrder.size() >= ONE_HUNDRED) {
                isAt = true;
            }
        }

        if (!sb.isEmpty()) {
            if (isAt) {
                warningService.warn(sb.toString(), warningAt.split(","));
            } else {
                warningService.warn(sb.toString());
            }
        }

        logger.info("warningJob end,{}",sb);
    }

    public String message(LocalDateTime start, LocalDateTime end, String table, Integer number,String result) {
        StringBuilder string = new StringBuilder();
        string.append(DateUtil.formatLocalDateTime(start)).append(" ~ ").append(DateUtil.formatLocalDateTime(end))
            .append("\n")
            .append("cash-flow.").append(table).append("不是最终态条数为:").append(number).append(";单号：").append(result);
        return string.toString();
    }
}
