package com.maguo.loan.cash.flow.job.overdue;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.DateUtil;

import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Classname RepayPlanOverdueDueBatch
 * @Description 还款计划逾期处理
 * @Date 2023/10/26 18:05
 * @Created by gale
 */
@Component
@JobHandler("repayPlanOverdueDueBatch")
public class RepayPlanOverdueDueBatch extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepayPlanOverdueDueBatch.class);

    /**
     * 咨询费罚息费率
     */
    public static final BigDecimal CONSULT_OVERDUE_RATE = new BigDecimal("0.0985");

    /**
     * 罚息费率为：23.99%/360的资方，咨询费罚息费率
     */
    public static final BigDecimal SPECIAL_CONSULT_OVERDUE_RATE = new BigDecimal("0.067");

    private static final BigDecimal PERCENT = BigDecimal.valueOf(100);

    @Autowired
    private MqService mqService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    private static final Integer PAGE_SIZE = 5000;
    @Autowired
    private LoanRepository loanRepository;
    @Value("${hxbk.overDueDayAdd}")
    private Integer hxbkOverDueDayAdd;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("RepayPlanOverdueDueBatch jobParam:{}", JsonUtil.toJsonString(jobParam));

        LocalDate date;
        List<String> loanIds = null;
        if (jobParam == null) {
            date = LocalDate.now();
        } else {
            date = jobParam.getStartDate() == null ? LocalDate.now() : jobParam.getStartDate();
            loanIds = jobParam.getLoanIds();
        }

        AtomicInteger pageNumber = new AtomicInteger(0);


        while (true) {
            PageRequest pageRequest = PageRequest.of(pageNumber.getAndIncrement(), PAGE_SIZE, Sort.by(Sort.Direction.ASC, "id"));
            List<RepayPlan> repayPlanList = null;
            if (CollectionUtil.isEmpty(loanIds)) {
                repayPlanList = repayPlanRepository.findByPlanRepayDateBeforeAndCustRepayState(
                    date, RepayState.NORMAL, pageRequest);
            } else {
                repayPlanList = repayPlanRepository.findByLoanIdIn(new HashSet<>(loanIds));
            }

            if (repayPlanList.isEmpty()) {
                break;
            }
            Map<String, List<RepayPlan>> repayPlanMaps = repayPlanList.stream().collect(Collectors.groupingBy(RepayPlan::getLoanId, Collectors.collectingAndThen(
                Collectors.toList(),
                list -> list.stream()
                    .sorted(Comparator.comparingInt(RepayPlan::getPeriod))
                    .collect(Collectors.toList())
            )));

            /**
             * 第一次逾期有宽限期  逾期未归还情况下后面期数继续逾期就没有宽限期了，如果第一期逾期归还了，则后续宽限期重新计算
             */
            for (Map.Entry<String, List<RepayPlan>> entry : repayPlanMaps.entrySet()) {
                List<RepayPlan> loanRepayPlanList = entry.getValue();
                Loan loan = loanRepository.findById(entry.getKey()).orElseThrow();
                //权益客户不用跑罚息
                if(FlowChannel.LVXIN.equals(loan.getFlowChannel())&& IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())){
                    continue;
                }
                //长银逾期
                if (loan.getBankChannel() == BankChannel.CYBK) {
                    for (int i = 0; i < loanRepayPlanList.size(); i++) {
                        RepayPlan repayPlan = loanRepayPlanList.get(i);
                        //逾期天数
                        long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
                        if (i == 0) {
                            //宽限期
                            int graceDay = QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay();
                            if (overDay <= graceDay) {
                                continue;
                            }
                        }
                        //应还罚息
                        BigDecimal penaltyAmt = totalAmtPenaltyAmt(loan, repayPlan, overDay);
                        if (BigDecimal.ZERO.compareTo(penaltyAmt) == 0) {
                            continue;
                        }

                        repayPlan.setPenaltyAmt(penaltyAmt.add(safeNum(repayPlan.getActPenaltyAmt())));
                        repayPlan.setAmount(
                            AmountUtil.sum(repayPlan.getPrincipalAmt(), repayPlan.getInterestAmt(),
                                repayPlan.getPenaltyAmt(), repayPlan.getGuaranteeAmt(), repayPlan.getConsultFee()));
                        repayPlanRepository.save(repayPlan);

                        billCallback(repayPlan, loan);
                    }
                //湖消逾期
                }else if(loan.getBankChannel()==BankChannel.HXBK){
                    for (int i = 0; i < loanRepayPlanList.size(); i++) {
                        RepayPlan repayPlan = loanRepayPlanList.get(i);
                        //宽限期 年结期间，湖消的逾期天数会增加
                        int graceDay =hxbkOverDueDayAdd==null?QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay():QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay()+hxbkOverDueDayAdd;
                        //逾期天数
                        long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
                            if (overDay <= graceDay) {
                                continue;
                            }
                        //应还罚息
                        BigDecimal penaltyAmt = totalAmtPenaltyAmt(loan, repayPlan, overDay);
                        if (BigDecimal.ZERO.compareTo(penaltyAmt) == 0) {
                            continue;
                        }

                        repayPlan.setPenaltyAmt(penaltyAmt.add(safeNum(repayPlan.getActPenaltyAmt())));
                        repayPlan.setAmount(
                            AmountUtil.sum(repayPlan.getPrincipalAmt(), repayPlan.getInterestAmt(),
                                repayPlan.getPenaltyAmt(), repayPlan.getGuaranteeAmt(), repayPlan.getConsultFee()));
                        repayPlanRepository.save(repayPlan);

                        billCallback(repayPlan, loan);
                    }
                }
            }
    /*        for (RepayPlan repayPlan : repayPlanList) {
                Loan loan = loanRepository.findById(repayPlan.getLoanId()).orElseThrow();
                //宽限期
                int graceDay = QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay();
                //逾期天数
                long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
                if (overDay <= graceDay) {
                    continue;
                }

                //应还罚息
                BigDecimal penaltyAmt = totalAmtPenaltyAmt(loan, repayPlan, overDay);
                if (BigDecimal.ZERO.compareTo(penaltyAmt) == 0) {
                    continue;
                }

                repayPlan.setPenaltyAmt(penaltyAmt.add(safeNum(repayPlan.getActPenaltyAmt())));
                repayPlan.setAmount(
                    AmountUtil.sum(repayPlan.getPrincipalAmt(), repayPlan.getInterestAmt(),
                        repayPlan.getPenaltyAmt(), repayPlan.getGuaranteeAmt(), repayPlan.getConsultFee()));
                repayPlanRepository.save(repayPlan);

                billCallback(repayPlan, loan);
            }*/
        }
    }

    /**
     * 逾期回调
     */
    private void billCallback(RepayPlan repayPlan, Loan loan) {
        String repayPlanId = repayPlan.getId();

        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setFlowChannel(loan.getFlowChannel());
        callBackDTO.setBusinessId(repayPlanId);
        callBackDTO.setCallbackState(CallbackState.OVERDUE);
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));

    }

    /**
     * 计算对客罚息
     * 罚息金额 = 逾期未还本金*0.0985%*逾期天数
     */
    private BigDecimal totalAmtPenaltyAmt(Loan loan, RepayPlan repayPlan, long overDay) {
        //剩余未还本金
        BigDecimal totalAmtBase = AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt());
        if (totalAmtBase.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal consultOverdueRate = CONSULT_OVERDUE_RATE;
        return totalAmtBase.multiply(new BigDecimal(overDay)).multiply(consultOverdueRate.divide(PERCENT)).setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal safeNum(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }
}
