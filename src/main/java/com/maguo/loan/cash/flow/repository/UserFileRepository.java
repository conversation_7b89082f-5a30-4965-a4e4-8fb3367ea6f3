package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


public interface UserFileRepository extends JpaRepository<UserFile, String> {

    List<UserFile> findByUserIdAndLoanStage(String userId, LoanStage stage);

    UserFile findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(String userId, LoanStage stage, FileType fileType);

    UserFile findTopByUserIdAndFileTypeOrderByUpdatedTimeDesc(String userId, FileType fileType);

    List<UserFile> findByUserIdAndFileTypeIn(String userId, List<FileType> fileTypes);

    List<UserFile> findByLoanStageAndUserIdInAndFileTypeIn(LoanStage loanStage, List<String> userIds, List<FileType> fileTypes);

    boolean existsByUserIdAndFileType(String userId, FileType fileType);

    List<UserFile> findByFileTypeInAndIdIn(List<String> fileTypeList, List<String> signApplyIds);

    List<UserFile> findAllByUserIdIn(List<String> userIds);

    List<UserFile> findByUserId(String userId);


    List<UserFile> findByLoanNoAndFileType(String loanId,FileType fileType);
}
