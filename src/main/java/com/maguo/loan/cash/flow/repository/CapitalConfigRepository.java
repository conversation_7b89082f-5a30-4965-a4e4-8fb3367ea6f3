package com.maguo.loan.cash.flow.repository;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * 资金配置
 */
public interface CapitalConfigRepository extends JpaRepository<CapitalConfig, String> {

    Optional<CapitalConfig> findByBankChannel(BankChannel bankChannel);

}
